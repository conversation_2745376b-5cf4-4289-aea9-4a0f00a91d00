# Api-register 規則文件 v1.0

此 @test.html 頁面(端點)允許新用戶註冊系統帳戶。註冊時需要提供用戶名、電子郵件、密碼和全名。
請參照我提供的具體資料，完成以下任務：

**密碼要求：**
- 長度至少8個字符
- 包含至少一個大寫字母
- 包含至少一個小寫字母
- 包含至少一個數字
- 包含至少一個特殊字符

**用戶名要求：**
- 長度在3-50個字符之間
- 只能包含字母、數字和下劃線
- 必須唯一

**成功響應：**
- 狀態碼：201 Created
- 返回用戶基本信息和創建時間
- 回應範例：
{
  "success": true,
  "message": "用戶註冊成功",
  "data": {
    "user_id": "550e8400-e29b-41d4-a716-446655440000",
    "username": "john_doe",
    "email": "<EMAIL>",
    "full_name": "<PERSON> Doe",
    "created_at": "2025-07-11T10:00:00Z"
  },
  "timestamp": "2025-07-11T10:00:00Z",
  "request_id": "req-123456"
}

**可能的錯誤：**
- 400 Bad Request：用戶名已存在或輸入驗證失敗
- 回應的範例：
{
  "success": false,
  "error": {
    "code": "USER_ALREADY_EXISTS",
    "message": "用戶名已存在",
    "details": {
      "username": "john_doe"
    }
  },
  "timestamp": "2025-07-11T10:00:00Z",
  "request_id": "req-123456"
}
- 422 Unprocessable Entity：請求格式錯誤
- 回應的範例：
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "輸入驗證失敗",
    "details": {
      "field": "password",
      "reason": "密碼必須包含至少一個大寫字母"
    }
  },
  "timestamp": "2025-07-11T10:00:00Z",
  "request_id": "req-123456"
}

##具體資料
- 伺服器位置：192.168.1.15:8000
- 請求位置：/api/v1/auth/register
- 請求方式：post
- 請求範例：
{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "full_name": "John Doe"
}
