<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用戶註冊 - 即時翻譯系統</title>

    <!-- 設計權杖和組件樣式 -->
    <link rel="stylesheet" href="design-tokens/tokens.css">
    <link rel="stylesheet" href="design-tokens/components.css">
    <link rel="stylesheet" href="style.css">

    <!-- 字體載入 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@100;300;400;500;600;700;800;900&display=swap">

    <!-- Material Symbols 圖示 -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />

    <style>
        /* ===== 註冊頁面專用樣式 ===== */
        body {
            font-family: 'Noto Sans', sans-serif;
            background-color: var(--md-ref-palette-neutral-50);
            color: var(--md-ref-palette-neutral-900);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .register-container {
            width: 100%;
            max-width: 480px;
        }

        .register-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .register-header {
            background: linear-gradient(135deg, var(--md-ref-palette-primary-600), var(--md-ref-palette-accent-500));
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .register-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .register-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 0.5rem;
        }

        .register-form {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--md-ref-palette-neutral-700);
            margin-bottom: 0.5rem;
        }

        .form-label.required::after {
            content: ' *';
            color: var(--md-ref-palette-error-500);
        }

        .input-wrapper {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--md-ref-palette-neutral-300);
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
            background-color: white;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--md-ref-palette-primary-600);
            box-shadow: 0 0 0 3px rgba(var(--md-ref-palette-primary-600-rgb), 0.1);
        }

        .form-input.error {
            border-color: var(--md-ref-palette-error-500);
        }

        .form-input.success {
            border-color: var(--md-ref-palette-success-500);
        }

        .input-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 18px;
            color: var(--md-ref-palette-neutral-500);
        }

        .input-icon.success {
            color: var(--md-ref-palette-success-500);
        }

        .input-icon.error {
            color: var(--md-ref-palette-error-500);
        }

        .form-error {
            font-size: 12px;
            color: var(--md-ref-palette-error-500);
            margin-top: 0.25rem;
            display: none;
        }

        .form-error.show {
            display: block;
        }

        .password-requirements {
            font-size: 12px;
            color: var(--md-ref-palette-neutral-600);
            margin-top: 0.5rem;
        }

        .requirement {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
        }

        .requirement-icon {
            font-size: 14px;
            color: var(--md-ref-palette-neutral-400);
        }

        .requirement.valid .requirement-icon {
            color: var(--md-ref-palette-success-500);
        }

        .submit-btn {
            width: 100%;
            padding: 12px;
            background: var(--md-ref-palette-primary-600);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 1rem;
        }

        .submit-btn:hover:not(:disabled) {
            background: var(--md-ref-palette-primary-700);
        }

        .submit-btn:disabled {
            background: var(--md-ref-palette-neutral-300);
            cursor: not-allowed;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success-message, .error-message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 14px;
            display: none;
        }

        .success-message {
            background: var(--md-ref-palette-success-100);
            color: var(--md-ref-palette-success-700);
            border: 1px solid var(--md-ref-palette-success-300);
        }

        .error-message {
            background: var(--md-ref-palette-error-100);
            color: var(--md-ref-palette-error-700);
            border: 1px solid var(--md-ref-palette-error-300);
        }

        .login-link {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--md-ref-palette-neutral-200);
            font-size: 14px;
            color: var(--md-ref-palette-neutral-600);
        }

        .login-link a {
            color: var(--md-ref-palette-primary-600);
            text-decoration: none;
            font-weight: 500;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        /* 響應式設計 */
        @media (max-width: 480px) {
            .register-header {
                padding: 1.5rem;
            }

            .register-form {
                padding: 1.5rem;
            }

            .register-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-card">
            <!-- 標題區域 -->
            <header class="register-header">
                <h1 class="register-title">建立新帳戶</h1>
                <p class="register-subtitle">加入即時翻譯系統，開始您的多語言會議體驗</p>
            </header>

            <!-- 註冊表單 -->
            <form class="register-form" id="registerForm">
                <!-- 成功/錯誤訊息 -->
                <div class="success-message" id="successMessage"></div>
                <div class="error-message" id="errorMessage"></div>

                <!-- 用戶名 -->
                <div class="form-group">
                    <label for="username" class="form-label required">用戶名</label>
                    <div class="input-wrapper">
                        <input
                            type="text"
                            id="username"
                            name="username"
                            class="form-input"
                            placeholder="請輸入用戶名 (3-50字符)"
                            required
                            autocomplete="username"
                        >
                        <span class="input-icon material-symbols-outlined" id="usernameIcon"></span>
                    </div>
                    <div class="form-error" id="usernameError"></div>
                </div>

                <!-- 電子郵件 -->
                <div class="form-group">
                    <label for="email" class="form-label required">電子郵件</label>
                    <div class="input-wrapper">
                        <input
                            type="email"
                            id="email"
                            name="email"
                            class="form-input"
                            placeholder="請輸入電子郵件地址"
                            required
                            autocomplete="email"
                        >
                        <span class="input-icon material-symbols-outlined" id="emailIcon"></span>
                    </div>
                    <div class="form-error" id="emailError"></div>
                </div>

                <!-- 全名 -->
                <div class="form-group">
                    <label for="fullName" class="form-label required">全名</label>
                    <div class="input-wrapper">
                        <input
                            type="text"
                            id="fullName"
                            name="full_name"
                            class="form-input"
                            placeholder="請輸入您的全名"
                            required
                            autocomplete="name"
                        >
                        <span class="input-icon material-symbols-outlined" id="fullNameIcon"></span>
                    </div>
                    <div class="form-error" id="fullNameError"></div>
                </div>

                <!-- 密碼 -->
                <div class="form-group">
                    <label for="password" class="form-label required">密碼</label>
                    <div class="input-wrapper">
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="form-input"
                            placeholder="請輸入密碼"
                            required
                            autocomplete="new-password"
                        >
                        <span class="input-icon material-symbols-outlined" id="passwordIcon"></span>
                    </div>
                    <div class="form-error" id="passwordError"></div>

                    <!-- 密碼要求 -->
                    <div class="password-requirements">
                        <div class="requirement" id="lengthReq">
                            <span class="requirement-icon material-symbols-outlined">radio_button_unchecked</span>
                            <span>至少 8 個字符</span>
                        </div>
                        <div class="requirement" id="uppercaseReq">
                            <span class="requirement-icon material-symbols-outlined">radio_button_unchecked</span>
                            <span>包含大寫字母</span>
                        </div>
                        <div class="requirement" id="lowercaseReq">
                            <span class="requirement-icon material-symbols-outlined">radio_button_unchecked</span>
                            <span>包含小寫字母</span>
                        </div>
                        <div class="requirement" id="numberReq">
                            <span class="requirement-icon material-symbols-outlined">radio_button_unchecked</span>
                            <span>包含數字</span>
                        </div>
                        <div class="requirement" id="specialReq">
                            <span class="requirement-icon material-symbols-outlined">radio_button_unchecked</span>
                            <span>包含特殊字符</span>
                        </div>
                    </div>
                </div>

                <!-- 提交按鈕 -->
                <button type="submit" class="submit-btn" id="submitBtn">
                    <span class="loading-spinner" id="loadingSpinner"></span>
                    <span id="submitText">建立帳戶</span>
                </button>

                <!-- 登入連結 -->
                <div class="login-link">
                    已有帳戶？ <a href="login.html">立即登入</a>
                </div>
            </form>
        </div>
    </div>

    <script>
        // ===== 註冊頁面 JavaScript 功能 =====

        // DOM 元素
        const form = document.getElementById('registerForm');
        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');
        const loadingSpinner = document.getElementById('loadingSpinner');
        const successMessage = document.getElementById('successMessage');
        const errorMessage = document.getElementById('errorMessage');

        // 輸入欄位
        const usernameInput = document.getElementById('username');
        const emailInput = document.getElementById('email');
        const fullNameInput = document.getElementById('fullName');
        const passwordInput = document.getElementById('password');

        // 圖示元素
        const usernameIcon = document.getElementById('usernameIcon');
        const emailIcon = document.getElementById('emailIcon');
        const fullNameIcon = document.getElementById('fullNameIcon');
        const passwordIcon = document.getElementById('passwordIcon');

        // 錯誤訊息元素
        const usernameError = document.getElementById('usernameError');
        const emailError = document.getElementById('emailError');
        const fullNameError = document.getElementById('fullNameError');
        const passwordError = document.getElementById('passwordError');

        // 密碼要求元素
        const lengthReq = document.getElementById('lengthReq');
        const uppercaseReq = document.getElementById('uppercaseReq');
        const lowercaseReq = document.getElementById('lowercaseReq');
        const numberReq = document.getElementById('numberReq');
        const specialReq = document.getElementById('specialReq');

        // API 設定
        const API_BASE_URL = 'http://192.168.1.15:8000';
        const REGISTER_ENDPOINT = '/api/v1/auth/register';

        // ===== 驗證函數 =====

        // 用戶名驗證
        function validateUsername(username) {
            const errors = [];

            if (username.length < 3 || username.length > 50) {
                errors.push('用戶名長度必須在 3-50 個字符之間');
            }

            if (!/^[a-zA-Z0-9_]+$/.test(username)) {
                errors.push('用戶名只能包含字母、數字和下劃線');
            }

            return errors;
        }

        // 電子郵件驗證
        function validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const errors = [];

            if (!emailRegex.test(email)) {
                errors.push('請輸入有效的電子郵件地址');
            }

            return errors;
        }

        // 全名驗證
        function validateFullName(fullName) {
            const errors = [];

            if (fullName.trim().length < 2) {
                errors.push('全名至少需要 2 個字符');
            }

            if (fullName.trim().length > 100) {
                errors.push('全名不能超過 100 個字符');
            }

            return errors;
        }

        // 密碼驗證
        function validatePassword(password) {
            const errors = [];
            const requirements = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password),
                special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
            };

            if (!requirements.length) {
                errors.push('密碼長度至少需要 8 個字符');
            }
            if (!requirements.uppercase) {
                errors.push('密碼必須包含至少一個大寫字母');
            }
            if (!requirements.lowercase) {
                errors.push('密碼必須包含至少一個小寫字母');
            }
            if (!requirements.number) {
                errors.push('密碼必須包含至少一個數字');
            }
            if (!requirements.special) {
                errors.push('密碼必須包含至少一個特殊字符');
            }

            return { errors, requirements };
        }

        // ===== UI 更新函數 =====

        // 更新欄位狀態
        function updateFieldStatus(input, icon, errorElement, errors) {
            if (errors.length === 0) {
                input.classList.remove('error');
                input.classList.add('success');
                icon.textContent = 'check_circle';
                icon.className = 'input-icon material-symbols-outlined success';
                errorElement.classList.remove('show');
            } else {
                input.classList.remove('success');
                input.classList.add('error');
                icon.textContent = 'error';
                icon.className = 'input-icon material-symbols-outlined error';
                errorElement.textContent = errors[0];
                errorElement.classList.add('show');
            }
        }

        // 更新密碼要求狀態
        function updatePasswordRequirements(requirements) {
            const reqElements = {
                length: lengthReq,
                uppercase: uppercaseReq,
                lowercase: lowercaseReq,
                number: numberReq,
                special: specialReq
            };

            Object.keys(requirements).forEach(key => {
                const element = reqElements[key];
                const icon = element.querySelector('.requirement-icon');

                if (requirements[key]) {
                    element.classList.add('valid');
                    icon.textContent = 'check_circle';
                } else {
                    element.classList.remove('valid');
                    icon.textContent = 'radio_button_unchecked';
                }
            });
        }

        // 顯示訊息
        function showMessage(type, message) {
            hideMessages();

            if (type === 'success') {
                successMessage.textContent = message;
                successMessage.style.display = 'block';
            } else {
                errorMessage.textContent = message;
                errorMessage.style.display = 'block';
            }
        }

        // 隱藏訊息
        function hideMessages() {
            successMessage.style.display = 'none';
            errorMessage.style.display = 'none';
        }

        // 設定載入狀態
        function setLoading(loading) {
            if (loading) {
                submitBtn.disabled = true;
                loadingSpinner.style.display = 'inline-block';
                submitText.textContent = '建立中...';
            } else {
                submitBtn.disabled = false;
                loadingSpinner.style.display = 'none';
                submitText.textContent = '建立帳戶';
            }
        }

        // ===== 事件監聽器 =====

        // 用戶名即時驗證
        usernameInput.addEventListener('input', function() {
            const errors = validateUsername(this.value);
            updateFieldStatus(this, usernameIcon, usernameError, errors);
        });

        // 電子郵件即時驗證
        emailInput.addEventListener('input', function() {
            const errors = validateEmail(this.value);
            updateFieldStatus(this, emailIcon, emailError, errors);
        });

        // 全名即時驗證
        fullNameInput.addEventListener('input', function() {
            const errors = validateFullName(this.value);
            updateFieldStatus(this, fullNameIcon, fullNameError, errors);
        });

        // 密碼即時驗證
        passwordInput.addEventListener('input', function() {
            const validation = validatePassword(this.value);
            updateFieldStatus(this, passwordIcon, passwordError, validation.errors);
            updatePasswordRequirements(validation.requirements);
        });

        // ===== API 整合 =====

        // 註冊 API 呼叫
        async function registerUser(userData) {
            try {
                const response = await fetch(`${API_BASE_URL}${REGISTER_ENDPOINT}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });

                const data = await response.json();

                if (response.ok) {
                    return { success: true, data };
                } else {
                    return { success: false, error: data };
                }
            } catch (error) {
                return {
                    success: false,
                    error: {
                        message: '網絡連接錯誤，請檢查您的網絡連接並重試',
                        details: error.message
                    }
                };
            }
        }

        // 表單提交處理
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            hideMessages();

            // 獲取表單數據
            const formData = new FormData(this);
            const userData = {
                username: formData.get('username').trim(),
                email: formData.get('email').trim(),
                password: formData.get('password'),
                full_name: formData.get('full_name').trim()
            };

            // 前端驗證
            const usernameErrors = validateUsername(userData.username);
            const emailErrors = validateEmail(userData.email);
            const fullNameErrors = validateFullName(userData.full_name);
            const passwordValidation = validatePassword(userData.password);

            // 更新所有欄位狀態
            updateFieldStatus(usernameInput, usernameIcon, usernameError, usernameErrors);
            updateFieldStatus(emailInput, emailIcon, emailError, emailErrors);
            updateFieldStatus(fullNameInput, fullNameIcon, fullNameError, fullNameErrors);
            updateFieldStatus(passwordInput, passwordIcon, passwordError, passwordValidation.errors);
            updatePasswordRequirements(passwordValidation.requirements);

            // 檢查是否有驗證錯誤
            const hasErrors = usernameErrors.length > 0 ||
                            emailErrors.length > 0 ||
                            fullNameErrors.length > 0 ||
                            passwordValidation.errors.length > 0;

            if (hasErrors) {
                showMessage('error', '請修正表單中的錯誤後再提交');
                return;
            }

            // 開始註冊流程
            setLoading(true);

            try {
                const result = await registerUser(userData);

                if (result.success) {
                    // 註冊成功
                    showMessage('success', '帳戶建立成功！歡迎加入即時翻譯系統');

                    // 清空表單
                    form.reset();

                    // 清除所有狀態
                    [usernameInput, emailInput, fullNameInput, passwordInput].forEach(input => {
                        input.classList.remove('success', 'error');
                    });

                    [usernameIcon, emailIcon, fullNameIcon, passwordIcon].forEach(icon => {
                        icon.textContent = '';
                        icon.className = 'input-icon material-symbols-outlined';
                    });

                    [usernameError, emailError, fullNameError, passwordError].forEach(error => {
                        error.classList.remove('show');
                    });

                    // 重置密碼要求
                    updatePasswordRequirements({
                        length: false,
                        uppercase: false,
                        lowercase: false,
                        number: false,
                        special: false
                    });

                    // 3秒後跳轉到登入頁面
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 3000);

                } else {
                    // 註冊失敗
                    let errorMsg = '註冊失敗，請重試';

                    if (result.error && result.error.message) {
                        errorMsg = result.error.message;
                    }

                    // 處理特定錯誤
                    if (result.error && result.error.code === 'USER_ALREADY_EXISTS') {
                        errorMsg = '此用戶名已被使用，請選擇其他用戶名';
                        updateFieldStatus(usernameInput, usernameIcon, usernameError, [errorMsg]);
                    } else if (result.error && result.error.code === 'VALIDATION_ERROR') {
                        errorMsg = result.error.message;

                        // 根據錯誤欄位更新對應的輸入框
                        if (result.error.details && result.error.details.field) {
                            const field = result.error.details.field;
                            const reason = result.error.details.reason || errorMsg;

                            switch (field) {
                                case 'username':
                                    updateFieldStatus(usernameInput, usernameIcon, usernameError, [reason]);
                                    break;
                                case 'email':
                                    updateFieldStatus(emailInput, emailIcon, emailError, [reason]);
                                    break;
                                case 'password':
                                    updateFieldStatus(passwordInput, passwordIcon, passwordError, [reason]);
                                    break;
                                case 'full_name':
                                    updateFieldStatus(fullNameInput, fullNameIcon, fullNameError, [reason]);
                                    break;
                            }
                        }
                    }

                    showMessage('error', errorMsg);
                }
            } catch (error) {
                showMessage('error', '發生未預期的錯誤，請稍後再試');
                console.error('Registration error:', error);
            } finally {
                setLoading(false);
            }
        });

        // ===== 初始化 =====

        // 頁面載入完成後的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 聚焦到第一個輸入框
            usernameInput.focus();

            // 隱藏所有訊息
            hideMessages();

            console.log('註冊頁面初始化完成');
        });
    </script>
</body>
</html>