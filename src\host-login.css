/* STT-SaaS 主持人登入頁面樣式 */

/* 基礎變數 */
:root {
  --primary-color: #1976d2;
  --secondary-color: #424242;
  --background-primary: #f5f5f5;
  --text-primary: #212121;
  --text-secondary: #757575;
  --border-color: #e0e0e0;
  --shadow: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-elevated: 0 4px 8px rgba(0,0,0,0.15);
  --success-color: #4caf50;
  --error-color: #f44336;
}

/* 主容器 */
.host-login-main {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

/* 卡片樣式 */
.card {
  background: white;
  border-radius: 16px;
  box-shadow: var(--shadow-elevated);
  width: 100%;
  max-width: 400px;
  overflow: hidden;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片標題區域 */
.card-header {
  background: var(--primary-color);
  color: white;
  padding: 24px;
  text-align: center;
}

.card-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

/* 表單樣式 */
form {
  padding: 32px 24px 24px;
}

.form-group {
  margin-bottom: 24px;
}

/* 輸入框容器 */
.text-input-container,
.search-input-container {
  position: relative;
  width: 100%;
}

/* 文字輸入框 */
.input-text {
  width: 100%;
  padding: 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 16px;
  font-family: inherit;
  transition: all 0.2s ease;
  background: white;
}

.input-text:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.input-text::placeholder {
  color: var(--text-secondary);
}

/* 選擇框樣式 */
.input-select {
  width: 100%;
  padding: 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 16px;
  font-family: inherit;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 48px;
}

.input-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

/* 搜尋圖示 */
.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
  z-index: 1;
}

.input-select-with-search {
  padding-left: 48px;
}

/* 已選擇語言顯示區域 */
.selected-languages {
  margin-top: 16px;
  min-height: 60px;
  padding: 12px;
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.selected-languages.has-languages {
  border-style: solid;
  border-color: var(--primary-color);
  background: rgba(25, 118, 210, 0.05);
}

.selected-languages:empty::before {
  content: "選擇的語言將顯示在這裡";
  color: var(--text-secondary);
  font-style: italic;
}

/* 語言標籤 */
.language-chip {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--primary-color);
  color: white;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  animation: chipSlideIn 0.2s ease;
}

@keyframes chipSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.language-chip-remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.language-chip-remove:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 按鈕樣式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  width: 100%;
  font-family: inherit;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #1565c0;
  transform: translateY(-1px);
  box-shadow: var(--shadow-elevated);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* 隱藏類別 */
.hidden {
  display: none !important;
}

/* 錯誤狀態 */
.input-error {
  border-color: var(--error-color) !important;
  box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1) !important;
}

.error-message {
  color: var(--error-color);
  font-size: 14px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 成功狀態 */
.input-success {
  border-color: var(--success-color) !important;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1) !important;
}

/* 載入狀態 */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 響應式設計 */
@media (max-width: 480px) {
  .host-login-main {
    padding: 16px;
  }
  
  .card {
    max-width: none;
  }
  
  .card-header {
    padding: 20px 16px;
  }
  
  .card-title {
    font-size: 20px;
  }
  
  form {
    padding: 24px 16px 16px;
  }
}

/* Material Icons 支援 */
.material-symbols-outlined {
  font-variation-settings:
    'FILL' 0,
    'wght' 400,
    'GRAD' 0,
    'opsz' 24;
}

/* 導航按鈕 */
.nav-button {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  backdrop-filter: blur(10px);
}

.nav-button:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-elevated);
}
