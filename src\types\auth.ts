// STT-SaaS 認證相關類型定義

// 註冊請求數據
export interface RegisterRequest {
  username: string
  email: string
  password: string
  full_name: string
}

// 用戶基本信息
export interface User {
  user_id: string
  username: string
  email: string
  full_name: string
  created_at: string
}

// API 成功響應
export interface ApiSuccessResponse<T = any> {
  success: true
  message: string
  data: T
  timestamp: string
  request_id: string
}

// API 錯誤響應
export interface ApiErrorResponse {
  success: false
  error: {
    code: string
    message: string
    details?: {
      field?: string
      reason?: string
      username?: string
      [key: string]: any
    }
  }
  timestamp: string
  request_id: string
}

// 註冊成功響應
export type RegisterSuccessResponse = ApiSuccessResponse<User>

// 註冊響應聯合類型
export type RegisterResponse = RegisterSuccessResponse | ApiErrorResponse

// 表單驗證錯誤
export interface ValidationError {
  field: string
  message: string
}

// 密碼強度要求
export interface PasswordRequirements {
  length: boolean
  uppercase: boolean
  lowercase: boolean
  number: boolean
  special: boolean
}

// 密碼驗證結果
export interface PasswordValidation {
  errors: string[]
  requirements: PasswordRequirements
}

// 表單欄位狀態
export type FieldStatus = 'idle' | 'valid' | 'invalid'

// 表單狀態
export interface FormState {
  username: {
    value: string
    status: FieldStatus
    errors: string[]
  }
  email: {
    value: string
    status: FieldStatus
    errors: string[]
  }
  fullName: {
    value: string
    status: FieldStatus
    errors: string[]
  }
  password: {
    value: string
    status: FieldStatus
    errors: string[]
    requirements: PasswordRequirements
  }
}

// API 錯誤代碼
export enum ApiErrorCode {
  USER_ALREADY_EXISTS = 'USER_ALREADY_EXISTS',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 載入狀態
export interface LoadingState {
  isLoading: boolean
  message?: string
}

// 訊息類型
export type MessageType = 'success' | 'error' | 'warning' | 'info'

// 訊息狀態
export interface MessageState {
  type: MessageType
  message: string
  visible: boolean
}
