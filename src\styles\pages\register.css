/* STT-SaaS 註冊頁面樣式 */

/* 基礎變數 */
:root {
  --register-primary: #1976d2;
  --register-primary-dark: #1565c0;
  --register-accent: #42a5f5;
  --register-success: #4caf50;
  --register-error: #f44336;
  --register-warning: #ff9800;
  --register-neutral-50: #fafafa;
  --register-neutral-100: #f5f5f5;
  --register-neutral-200: #eeeeee;
  --register-neutral-300: #e0e0e0;
  --register-neutral-400: #bdbdbd;
  --register-neutral-500: #9e9e9e;
  --register-neutral-600: #757575;
  --register-neutral-700: #616161;
  --register-neutral-800: #424242;
  --register-neutral-900: #212121;
}

/* 主容器 */
.register-main {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: linear-gradient(135deg, var(--register-primary) 0%, var(--register-accent) 100%);
  font-family: 'Noto Sans', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', Roboto, sans-serif;
}

.register-container {
  width: 100%;
  max-width: 480px;
}

.register-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 標題區域 */
.register-header {
  background: linear-gradient(135deg, var(--register-primary), var(--register-accent));
  color: white;
  padding: 2rem;
  text-align: center;
}

.register-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.register-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin-top: 0.5rem;
  margin-bottom: 0;
}

/* 表單區域 */
.register-form {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--register-neutral-700);
  margin-bottom: 0.5rem;
}

.form-label.required::after {
  content: ' *';
  color: var(--register-error);
}

/* 輸入框樣式 */
.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  padding-right: 48px;
  border: 2px solid var(--register-neutral-300);
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  transition: all 0.2s ease;
  background-color: white;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: var(--register-primary);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.form-input.error {
  border-color: var(--register-error);
}

.form-input.error:focus {
  box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

.form-input.success {
  border-color: var(--register-success);
}

.form-input.success:focus {
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-input::placeholder {
  color: var(--register-neutral-500);
}

/* 輸入框圖示 */
.input-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: var(--register-neutral-500);
  pointer-events: none;
}

.input-icon.success {
  color: var(--register-success);
}

.input-icon.error {
  color: var(--register-error);
}

/* 錯誤訊息 */
.form-error {
  font-size: 12px;
  color: var(--register-error);
  margin-top: 0.25rem;
  display: none;
}

.form-error.show {
  display: block;
}

/* 密碼要求 */
.password-requirements {
  font-size: 12px;
  color: var(--register-neutral-600);
  margin-top: 0.5rem;
}

.requirement {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.requirement-icon {
  font-size: 14px;
  color: var(--register-neutral-400);
  transition: color 0.2s ease;
}

.requirement.valid .requirement-icon {
  color: var(--register-success);
}

.requirement.valid {
  color: var(--register-success);
}

/* 提交按鈕 */
.submit-btn {
  width: 100%;
  padding: 12px;
  background: var(--register-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.submit-btn:hover:not(:disabled) {
  background: var(--register-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.submit-btn:disabled {
  background: var(--register-neutral-400);
  cursor: not-allowed;
  transform: none;
}

/* 載入動畫 */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 訊息樣式 */
.message {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 14px;
  display: none;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message.show {
  display: block;
}

.success-message {
  background: rgba(76, 175, 80, 0.1);
  color: var(--register-success);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.error-message {
  background: rgba(244, 67, 54, 0.1);
  color: var(--register-error);
  border: 1px solid rgba(244, 67, 54, 0.3);
}

/* 登入連結 */
.login-link {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--register-neutral-200);
  font-size: 14px;
  color: var(--register-neutral-600);
}

.login-link a {
  color: var(--register-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.login-link a:hover {
  color: var(--register-primary-dark);
  text-decoration: underline;
}

/* 導航按鈕 */
.nav-button {
  position: fixed;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.9);
  color: var(--register-primary);
  border: 2px solid var(--register-primary);
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1001;
}

.nav-button:hover {
  background: var(--register-primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

/* 響應式設計 */
@media (max-width: 480px) {
  .register-main {
    padding: 0.5rem;
  }
  
  .register-header {
    padding: 1.5rem;
  }
  
  .register-form {
    padding: 1.5rem;
  }
  
  .register-title {
    font-size: 20px;
  }
  
  .nav-button {
    top: 10px;
    left: 10px;
    padding: 8px 12px;
    font-size: 12px;
  }
}

/* Material Icons 支援 */
.material-symbols-outlined {
  font-variation-settings:
    'FILL' 0,
    'wght' 400,
    'GRAD' 0,
    'opsz' 24;
}
