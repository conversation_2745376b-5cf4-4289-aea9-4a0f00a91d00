import { useState, useEffect, useCallback } from 'react'
import { useLocation, Link } from 'react-router-dom'
import '../translate.css'

interface LanguageTag {
  code: string
  name: string
  active: boolean
}

interface LocationState {
  selectedLanguages?: string[]
  meetingName?: string
}

function TranslatePage() {
  const location = useLocation()
  const state = location.state as LocationState
  
  const [isQrModalOpen, setIsQrModalOpen] = useState(false)
  const [languageTags, setLanguageTags] = useState<LanguageTag[]>([
    { code: 'en', name: 'English', active: true },
    { code: 'ja', name: '日本語', active: true },
    { code: 'zh', name: '繁體中文', active: true },
    { code: 'ko', name: '한국어', active: true }
  ])
  const [subtitleContent, setSubtitleContent] = useState(
    '各位貴賓，歡迎蒞臨本次國際會議。為了確保會議順利進行，請務必保持手機靜音或震動模式。會議期間請勿隨意走動或交談，感謝配合。今天的會議預計到四點左右，隨時有問題都可以向身旁的工作人員詢問。'
  )

  // 根據從主頁傳來的語言設置初始化語言標籤
  useEffect(() => {
    if (state?.selectedLanguages && state.selectedLanguages.length > 0) {
      const languageMap: Record<string, string> = {
        'en': 'English',
        'ja': '日本語',
        'zh': '繁體中文',
        'ko': '한국어',
        'fr': 'Français',
        'de': 'Deutsch',
        'es': 'Español',
        'it': 'Italiano',
        'pt': 'Português',
        'ru': 'Русский'
      }

      const newLanguageTags = state.selectedLanguages.map(code => ({
        code,
        name: languageMap[code] || code,
        active: true
      }))

      setLanguageTags(newLanguageTags)
    }
  }, [state])

  // 處理 QR Code 模態框
  const openQrModal = useCallback(() => {
    setIsQrModalOpen(true)
    document.body.style.overflow = 'hidden'
  }, [])

  const closeQrModal = useCallback(() => {
    setIsQrModalOpen(false)
    document.body.style.overflow = ''
  }, [])

  // 處理語言標籤切換
  const toggleLanguageTag = useCallback((code: string) => {
    setLanguageTags(prev => 
      prev.map(tag => 
        tag.code === code ? { ...tag, active: !tag.active } : tag
      )
    )
  }, [])

  // 處理按鈕點擊
  const handleLanguageSettings = useCallback(() => {
    alert('語言設定功能開發中...')
  }, [])

  const handleLeaveMeeting = useCallback(() => {
    if (confirm('確定要離開會議嗎？')) {
      // 可以導航回主頁或執行其他邏輯
      window.location.href = '/'
    }
  }, [])

  // ESC 鍵關閉模態框
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isQrModalOpen) {
        closeQrModal()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isQrModalOpen, closeQrModal])

  // 模擬即時字幕更新
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date().toLocaleTimeString()
      setSubtitleContent(prev => `${prev} [${now}] 新的即時字幕內容...`)
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  // 根據語言代碼獲取翻譯內容
  const getTranslationContent = (langCode: string) => {
    const translations: Record<string, JSX.Element> = {
      'en': (
        <div className="translation-content">
          <p>During the meeting, please refrain from moving around or engaging in conversation.</p>
          <p>Thank you for your cooperation. Today's session is expected to last until around 4 PM.</p>
          <p><strong>If you have any questions, feel free to ask the staff nearby.</strong></p>
        </div>
      ),
      'ja': (
        <div className="translation-content">
          <p>会議中は席を立ったり、私語を控えていただきますようお願いいたします。</p>
          <p>ご協力ありがとうございます。本日の会議は午後4時頃まで予定しております。</p>
          <p><strong>ご不明な点がございましたら、近くのスタッフにお尋ねください。</strong></p>
        </div>
      ),
      'zh': (
        <div className="translation-content">
          <p>會議期間請勿隨意走動，請務必保持安靜。</p>
          <p>會議期間請勿隨意走動或交談，感謝配合。今天的會議預計到四點左右。</p>
          <p><strong>隨時有問題都可以向身旁的工作人員詢問。</strong></p>
        </div>
      ),
      'ko': (
        <div className="translation-content">
          <p>회의에 주시기 바라며 대화를 삼가 주시기 바랍니다.</p>
          <p>협조해 주셔서 감사합니다. 오늘 회의는 오후 4시쯤까지 진행될 예정입니다.</p>
          <p><strong>궁금한 점이 있으시면 가까운 스태프에게 언제든지 문의해 주세요.</strong></p>
        </div>
      )
    }

    return translations[langCode] || (
      <div className="translation-content">
        <p>Translation content for {langCode} will be displayed here.</p>
      </div>
    )
  }

  return (
    <main className="translate-main">
      {/* 返回主頁按鈕 */}
      <Link to="/" className="nav-button" style={{ top: '20px', left: '20px' }}>
        <span className="material-symbols-outlined">home</span>
        返回主頁
      </Link>

      {/* 1. 頂部控制按鈕區 */}
      <section className="control-buttons">
        <div className="app-bar">
          <button className="btn btn-secondary" onClick={handleLanguageSettings}>
            <span className="material-symbols-outlined">translate</span>
            語言設定
          </button>
          <header className="app-header">
            <h1>{state?.meetingName || '即時翻譯'}</h1>
          </header>
          <button className="btn btn-secondary" onClick={handleLeaveMeeting}>
            <span className="material-symbols-outlined">logout</span>
            結束會議
          </button>
        </div>
      </section>

      {/* 2. 已選擇語言標籤區 */}
      <section className="tool-bar">
        <div className="language-tags">
          {languageTags.map(tag => (
            <span
              key={tag.code}
              className={`tab-item ${tag.active ? 'tab-item-active' : ''} language-tag`}
              onClick={() => toggleLanguageTag(tag.code)}
            >
              {tag.name}
            </span>
          ))}
        </div>
      </section>

      {/* 3. 翻譯語言顯示區域 */}
      <section className="translation-display-area">
        <div className="translation-flex">
          {languageTags
            .filter(tag => tag.active)
            .map(tag => (
              <div key={tag.code} className="translation-column" data-lang={tag.code}>
                {getTranslationContent(tag.code)}
              </div>
            ))}
        </div>

        {/* 4. 即時字幕區域 */}
        <section className="live-subtitles">
          <h2 className="subtitle-title">即時字幕</h2>
          <div className="subtitle-content">
            <p>{subtitleContent}</p>
          </div>
        </section>
      </section>

      {/* 5. QR Code浮動按鈕 */}
      <button className="qr-code-btn" onClick={openQrModal}>
        <span className="material-symbols-outlined">qr_code_scanner</span>
      </button>

      {/* 6. QR Code 模態框 */}
      {isQrModalOpen && (
        <div 
          className="qr-code-card-overlay" 
          onClick={(e) => e.target === e.currentTarget && closeQrModal()}
          role="dialog" 
          aria-modal="true"
          aria-labelledby="qr-card-title"
        >
          <div className="qr-code-card" role="document">
            <header className="qr-card-header">
              <h2 className="qr-card-title" id="qr-card-title">會議連結</h2>
              <button 
                className="qr-card-close-btn" 
                onClick={closeQrModal}
                aria-label="關閉會議連結視窗" 
                type="button"
              >
                <span className="material-symbols-outlined" aria-hidden="true">close</span>
              </button>
            </header>

            <div className="qr-card-content">
              <div className="qr-code-container">
                <div className="qr-code-image" role="img" aria-label="會議連結 QR Code">
                  <div className="qr-code-placeholder">
                    <span className="material-symbols-outlined qr-placeholder-icon" aria-hidden="true">
                      qr_code
                    </span>
                  </div>
                </div>
              </div>

              <div className="scan-button-container">
                <button className="scan-me-btn" type="button" aria-label="掃描 QR Code 以加入會議">
                  <span className="material-symbols-outlined scan-icon" aria-hidden="true">smartphone</span>
                  <span className="scan-text">SCAN ME</span>
                </button>
              </div>

              <div className="url-display-container">
                <div className="input" role="textbox" aria-readonly="true" aria-label="會議連結網址">
                  <span className="md-title-small">https://www.fenrirdata.com</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </main>
  )
}

export default TranslatePage
