// STT-SaaS 表單驗證工具函數

import type { PasswordRequirements, PasswordValidation } from '../types/auth'

/**
 * 驗證用戶名
 * @param username 用戶名
 * @returns 錯誤訊息陣列
 */
export function validateUsername(username: string): string[] {
  const errors: string[] = []
  
  if (!username) {
    errors.push('用戶名為必填項目')
    return errors
  }
  
  if (username.length < 3 || username.length > 50) {
    errors.push('用戶名長度必須在 3-50 個字符之間')
  }
  
  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    errors.push('用戶名只能包含字母、數字和下劃線')
  }
  
  return errors
}

/**
 * 驗證電子郵件
 * @param email 電子郵件地址
 * @returns 錯誤訊息陣列
 */
export function validateEmail(email: string): string[] {
  const errors: string[] = []
  
  if (!email) {
    errors.push('電子郵件為必填項目')
    return errors
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  
  if (!emailRegex.test(email)) {
    errors.push('請輸入有效的電子郵件地址')
  }
  
  return errors
}

/**
 * 驗證全名
 * @param fullName 全名
 * @returns 錯誤訊息陣列
 */
export function validateFullName(fullName: string): string[] {
  const errors: string[] = []
  
  if (!fullName) {
    errors.push('全名為必填項目')
    return errors
  }
  
  const trimmedName = fullName.trim()
  
  if (trimmedName.length < 2) {
    errors.push('全名至少需要 2 個字符')
  }
  
  if (trimmedName.length > 100) {
    errors.push('全名不能超過 100 個字符')
  }
  
  return errors
}

/**
 * 驗證密碼
 * @param password 密碼
 * @returns 密碼驗證結果
 */
export function validatePassword(password: string): PasswordValidation {
  const errors: string[] = []
  
  if (!password) {
    errors.push('密碼為必填項目')
    return {
      errors,
      requirements: {
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false
      }
    }
  }
  
  const requirements: PasswordRequirements = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    number: /\d/.test(password),
    special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
  }
  
  if (!requirements.length) {
    errors.push('密碼長度至少需要 8 個字符')
  }
  
  if (!requirements.uppercase) {
    errors.push('密碼必須包含至少一個大寫字母')
  }
  
  if (!requirements.lowercase) {
    errors.push('密碼必須包含至少一個小寫字母')
  }
  
  if (!requirements.number) {
    errors.push('密碼必須包含至少一個數字')
  }
  
  if (!requirements.special) {
    errors.push('密碼必須包含至少一個特殊字符')
  }
  
  return { errors, requirements }
}

/**
 * 驗證所有表單欄位
 * @param data 表單數據
 * @returns 是否有錯誤
 */
export function validateForm(data: {
  username: string
  email: string
  fullName: string
  password: string
}): boolean {
  const usernameErrors = validateUsername(data.username)
  const emailErrors = validateEmail(data.email)
  const fullNameErrors = validateFullName(data.fullName)
  const passwordValidation = validatePassword(data.password)
  
  return (
    usernameErrors.length === 0 &&
    emailErrors.length === 0 &&
    fullNameErrors.length === 0 &&
    passwordValidation.errors.length === 0
  )
}

/**
 * 清理輸入值
 * @param value 輸入值
 * @returns 清理後的值
 */
export function sanitizeInput(value: string): string {
  return value.trim()
}

/**
 * 檢查密碼強度
 * @param password 密碼
 * @returns 密碼強度分數 (0-5)
 */
export function getPasswordStrength(password: string): number {
  const requirements = validatePassword(password).requirements
  
  return Object.values(requirements).filter(Boolean).length
}

/**
 * 獲取密碼強度描述
 * @param strength 密碼強度分數
 * @returns 強度描述
 */
export function getPasswordStrengthText(strength: number): string {
  switch (strength) {
    case 0:
    case 1:
      return '非常弱'
    case 2:
      return '弱'
    case 3:
      return '一般'
    case 4:
      return '強'
    case 5:
      return '非常強'
    default:
      return '未知'
  }
}
