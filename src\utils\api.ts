// STT-SaaS API 服務函數

import type { 
  RegisterRequest, 
  RegisterResponse, 
  ApiErrorResponse,
  ApiErrorCode 
} from '../types/auth'

// API 配置
const API_CONFIG = {
  BASE_URL: 'http://192.168.1.15:8000',
  ENDPOINTS: {
    REGISTER: '/api/v1/auth/register'
  },
  TIMEOUT: 10000 // 10 秒超時
}

/**
 * 創建 API 錯誤響應
 * @param code 錯誤代碼
 * @param message 錯誤訊息
 * @param details 錯誤詳情
 * @returns API 錯誤響應
 */
function createErrorResponse(
  code: string,
  message: string,
  details?: any
): ApiErrorResponse {
  return {
    success: false,
    error: {
      code,
      message,
      details
    },
    timestamp: new Date().toISOString(),
    request_id: `req-${Date.now()}`
  }
}

/**
 * 處理網絡錯誤
 * @param error 錯誤對象
 * @returns API 錯誤響應
 */
function handleNetworkError(error: any): ApiErrorResponse {
  console.error('Network error:', error)
  
  if (error.name === 'AbortError') {
    return createErrorResponse(
      'TIMEOUT_ERROR',
      '請求超時，請檢查網絡連接並重試',
      { timeout: API_CONFIG.TIMEOUT }
    )
  }
  
  return createErrorResponse(
    'NETWORK_ERROR',
    '網絡連接錯誤，請檢查您的網絡連接並重試',
    { originalError: error.message }
  )
}

/**
 * 發送 HTTP 請求
 * @param endpoint 端點路徑
 * @param options 請求選項
 * @returns Promise<Response>
 */
async function fetchWithTimeout(
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.TIMEOUT)
  
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}${endpoint}`, {
      ...options,
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers
      }
    })
    
    clearTimeout(timeoutId)
    return response
  } catch (error) {
    clearTimeout(timeoutId)
    throw error
  }
}

/**
 * 註冊新用戶
 * @param userData 用戶註冊數據
 * @returns Promise<RegisterResponse>
 */
export async function registerUser(userData: RegisterRequest): Promise<RegisterResponse> {
  try {
    // 驗證輸入數據
    if (!userData.username || !userData.email || !userData.password || !userData.full_name) {
      return createErrorResponse(
        'VALIDATION_ERROR',
        '所有欄位都是必填的',
        { missingFields: Object.keys(userData).filter(key => !userData[key as keyof RegisterRequest]) }
      )
    }
    
    // 發送註冊請求
    const response = await fetchWithTimeout(API_CONFIG.ENDPOINTS.REGISTER, {
      method: 'POST',
      body: JSON.stringify(userData)
    })
    
    // 解析響應
    const data = await response.json()
    
    if (response.ok) {
      // 註冊成功
      return data as RegisterResponse
    } else {
      // 註冊失敗，返回服務器錯誤
      return data as ApiErrorResponse
    }
    
  } catch (error: any) {
    // 處理網絡錯誤
    return handleNetworkError(error)
  }
}

/**
 * 檢查用戶名是否可用
 * @param username 用戶名
 * @returns Promise<boolean>
 */
export async function checkUsernameAvailability(username: string): Promise<boolean> {
  try {
    const response = await fetchWithTimeout(`/api/v1/auth/check-username`, {
      method: 'POST',
      body: JSON.stringify({ username })
    })
    
    if (response.ok) {
      const data = await response.json()
      return data.available === true
    }
    
    return false
  } catch (error) {
    console.error('Error checking username availability:', error)
    return false
  }
}

/**
 * 檢查電子郵件是否可用
 * @param email 電子郵件
 * @returns Promise<boolean>
 */
export async function checkEmailAvailability(email: string): Promise<boolean> {
  try {
    const response = await fetchWithTimeout(`/api/v1/auth/check-email`, {
      method: 'POST',
      body: JSON.stringify({ email })
    })
    
    if (response.ok) {
      const data = await response.json()
      return data.available === true
    }
    
    return false
  } catch (error) {
    console.error('Error checking email availability:', error)
    return false
  }
}

/**
 * 解析 API 錯誤訊息
 * @param error API 錯誤響應
 * @returns 用戶友好的錯誤訊息
 */
export function parseApiError(error: ApiErrorResponse): string {
  switch (error.error.code) {
    case 'USER_ALREADY_EXISTS':
      return '此用戶名已被使用，請選擇其他用戶名'
    
    case 'EMAIL_ALREADY_EXISTS':
      return '此電子郵件已被註冊，請使用其他電子郵件或嘗試登入'
    
    case 'VALIDATION_ERROR':
      return error.error.details?.reason || error.error.message || '輸入驗證失敗'
    
    case 'NETWORK_ERROR':
      return '網絡連接錯誤，請檢查您的網絡連接並重試'
    
    case 'TIMEOUT_ERROR':
      return '請求超時，請重試'
    
    case 'SERVER_ERROR':
      return '服務器錯誤，請稍後再試'
    
    default:
      return error.error.message || '發生未知錯誤，請稍後再試'
  }
}

/**
 * 獲取錯誤對應的欄位
 * @param error API 錯誤響應
 * @returns 錯誤對應的欄位名稱
 */
export function getErrorField(error: ApiErrorResponse): string | null {
  if (error.error.details?.field) {
    return error.error.details.field
  }
  
  if (error.error.code === 'USER_ALREADY_EXISTS') {
    return 'username'
  }
  
  if (error.error.code === 'EMAIL_ALREADY_EXISTS') {
    return 'email'
  }
  
  return null
}
