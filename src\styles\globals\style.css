/* ========================================
   STT-SaaS 主要樣式檔案
   統一引入設計權杖系統
   ======================================== */

/* 引入統一設計權杖檔案 */
@import url('./design-tokens/tokens.css');

/* ========================================
   專案特定樣式
   使用設計權杖系統
   ======================================== */

/* 主要容器 */
main {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: rgb(0, 0, 0, 25%);
}

/* 翻譯頁面主容器 */
main.translate-main {
    margin: 0 auto;
    padding: 0 32px;
    background-color: var(--md-sys-color-surface);
    justify-content: flex-start;
}

/* 應用程式標題 */
.app-header {
    display: flex;
    justify-content: center;
    align-items: center;
}

.translate-main .app-header {
    margin-bottom: 0;
}

/* ========================================
   翻譯頁面專用樣式
   ======================================== */

/* 控制按鈕區 */
.control-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 100%;
}

.control-buttons .btn .material-symbols-outlined {
    font-size: 1.2rem;
    padding-right: 0.5rem;
}

.language-tags {
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.9);
    padding: 12px 16px;
    border-radius: 60px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}
.logo-container{
    display: flex;
    align-items: center;
}
.logo-container img{
    width: 32.54px;
    height: 28px;
    margin-right: 1rem;
}

/* 翻譯顯示區域 */
.translation-display-area {
    flex: 1;
    max-width: 960px;
    margin-bottom: 2rem;
}

.translation-flex {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    gap: 2rem;
    height: 100%;
}

.translation-column {
    width: 25%;
    transition: all 0.2s ease;
}

.translation-column:hover {
    transform: translateY(-2px);
}

.translation-content {
    font-family: 'Noto Sans', 'Segoe UI', sans-serif;
    font-size: var(--md-sys-typescale-body-medium-size);
    line-height: 1.5;
    color: var(--md-sys-color-on-surface);
}

.translation-content p {
    margin-bottom: 1rem;
}

.translation-content p:last-child {
    margin-bottom: 0;
}

.translation-content strong {
    font-weight: 600;
    color: var(--md-sys-color-on-surface);
}

/* 即時字幕區域 */
.live-subtitles {
    height: 180px;
    max-width: 720px;
    margin: 0 auto 2rem;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.subtitle-title {
    font-family: 'Noto Sans', 'Segoe UI', sans-serif;
    font-size: var(--md-sys-typescale-title-small-size);
    font-weight: 500;
    color: var(--md-sys-color-on-surface);
    margin: 0 0 1rem 0;
    border-bottom: 2px solid var(--md-sys-color-primary);
    padding-bottom: 0.5rem;
}

.subtitle-content {
    flex: 1;
    overflow-y: auto;
    font-family: 'Noto Sans', 'Segoe UI', sans-serif;
    font-size: var(--md-sys-typescale-body-medium-size);
    line-height: 1.5;
    color: var(--md-sys-color-on-surface);
}

.subtitle-content p {
    margin: 0;
}

/* QR Code 浮動按鈕 */
.qr-code-btn {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--md-sys-color-primary);
    color: var(--md-sys-color-on-primary);
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.qr-code-btn:hover {
    background: var(--md-ref-palette-primary-700);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.qr-code-btn .material-symbols-outlined {
    font-size: 1.5rem;
}

/* 動畫效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.translation-column {
    animation: fadeInUp 0.6s ease-out;
}

.translation-column:nth-child(1) {
    animation-delay: 0.1s;
}

.translation-column:nth-child(2) {
    animation-delay: 0.2s;
}

.translation-column:nth-child(3) {
    animation-delay: 0.3s;
}

.translation-column:nth-child(4) {
    animation-delay: 0.4s;
}

/* .language-tag.active {
    animation: pulse 2s infinite;
} */

.qr-code-btn:active {
    transform: scale(0.95);
}



/* ========================================
   響應式設計
   ======================================== */
/* 平板、電腦 Landscape (> 1024px) */
@media (min-width: 1024px) {
    section {
        max-width: 960px;
        margin: 0 auto;
        /* 容器內距 (Gutter) 保持 16px */
    }
}

/* 平板 Portrait (744-1023px) */
@media (min-width: 744px) {
    section {
        margin: 0 32px;
    }

    main.bg-primary {
        padding: 0 1rem;
    }

    .translation-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .language-tags {
        flex-wrap: wrap;
        justify-content: center;
    }

    .live-subtitles {
        padding: 1rem;
    }

    .qr-code-btn {
        bottom: 1.5rem;
        right: 1.5rem;
        width: 48px;
        height: 48px;
    }
}

/* 手機 Landscape (600-900px) */
@media (min-width: 600px) and (max-width: 900px) {
    section {
        width: 100%;
    }
}

/* 手機 Portrait: 320-600px */
@media (min-width: 320px) and (max-width: 600px) {
    section {
        width: 100%;
        padding: 16px;
        margin: 0 16px;
        box-sizing: border-box;
    }

    main {
        padding: 1rem;
    }

    main.bg-primary {
        padding: 0 0.5rem;
    }

    .app-header {
        margin-bottom: 1rem;
    }

    /* .app-header h1 {
        font-size: var(--md-sys-typescale-headline-small-size);
    } */

    .control-buttons {
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .control-buttons .btn {
        width: 100%;
        justify-content: center;
    }

    .translation-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .translation-column {
        padding: 1rem;
    }

    .language-tags {
        padding: 0.25rem;
        gap: 0.25rem;
    }

    .language-tag {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .live-subtitles {
        height: 140px;
        padding: 1rem;
        margin: 0 0.5rem;
    }

    .subtitle-title {
        font-size: var(--md-sys-typescale-title-medium-size);
        margin-bottom: 0.75rem;
    }

    .qr-code-btn {
        bottom: 1rem;
        right: 1rem;
        width: 44px;
        height: 44px;
    }

    .qr-code-btn .material-symbols-outlined {
        font-size: 1.2rem;
    }

    .entry-form-container {
        padding: 1.5rem;
    }
}

/* 大螢幕優化 */
@media (min-width: 1200px) {
    .translation-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}