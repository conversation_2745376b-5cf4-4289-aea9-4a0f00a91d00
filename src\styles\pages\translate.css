/* STT-SaaS 翻譯頁面樣式 */

/* 基礎變數 */
:root {
  --primary-color: #1976d2;
  --secondary-color: #424242;
  --background-primary: #f5f5f5;
  --text-primary: #212121;
  --text-secondary: #757575;
  --border-color: #e0e0e0;
  --shadow: 0 2px 4px rgba(0,0,0,0.1);
  --shadow-elevated: 0 4px 8px rgba(0,0,0,0.15);
}

/* 重置和基礎樣式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Noto Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background-primary);
  color: var(--text-primary);
}

/* 主容器 */
.translate-main {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-primary);
}

/* 頂部控制按鈕區 */
.control-buttons {
  background: white;
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 100;
}

.app-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.app-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

/* 按鈕樣式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #616161;
  transform: translateY(-1px);
  box-shadow: var(--shadow-elevated);
}

/* 工具列 */
.tool-bar {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
}

.language-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  max-width: 1200px;
  margin: 0 auto;
}

.tab-item {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid var(--border-color);
  background: white;
}

.tab-item-active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.tab-item:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

/* 翻譯顯示區域 */
.translation-display-area {
  flex: 1;
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.translation-flex {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.translation-column {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease;
}

.translation-column:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-elevated);
}

.translation-content p {
  margin: 0 0 16px 0;
  line-height: 1.6;
  font-size: 16px;
}

.translation-content p:last-child {
  margin-bottom: 0;
}

.translation-content strong {
  font-weight: 600;
  color: var(--primary-color);
}

/* 即時字幕區域 */
.live-subtitles {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: var(--shadow);
  margin-top: 24px;
}

.subtitle-title {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.subtitle-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.subtitle-content p {
  margin: 0;
  line-height: 1.6;
  font-size: 16px;
}

/* QR Code 浮動按鈕 */
.qr-code-btn {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  border: none;
  cursor: pointer;
  box-shadow: var(--shadow-elevated);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.qr-code-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(0,0,0,0.2);
}

/* QR Code 模態框 */
.qr-code-card-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.qr-code-card-overlay.hidden {
  display: none;
}

.qr-code-card {
  background: white;
  border-radius: 16px;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 8px 32px rgba(0,0,0,0.2);
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.qr-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border-color);
}

.qr-card-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.qr-card-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.qr-card-close-btn:hover {
  background-color: #f5f5f5;
}

.qr-card-content {
  padding: 24px;
  text-align: center;
}

.qr-code-container {
  margin-bottom: 24px;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  background: #f8f9fa;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--border-color);
}

.qr-placeholder-icon {
  font-size: 64px;
  color: var(--text-secondary);
}

.scan-button-container {
  margin-bottom: 24px;
}

.scan-me-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
  transition: background-color 0.2s ease;
}

.scan-me-btn:hover {
  background-color: #1565c0;
}

.url-display-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
}

.md-title-small {
  font-size: 14px;
  color: var(--text-secondary);
  word-break: break-all;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .app-bar {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
  }
  
  .translation-flex {
    grid-template-columns: 1fr;
  }
  
  .translation-display-area {
    padding: 16px;
  }
  
  .qr-code-btn {
    bottom: 16px;
    right: 16px;
  }
}

/* Material Icons 支援 */
.material-symbols-outlined {
  font-variation-settings:
    'FILL' 0,
    'wght' 400,
    'GRAD' 0,
    'opsz' 24;
}

/* 導航按鈕 */
.nav-button {
  position: fixed;
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1001;
}

.nav-button:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-elevated);
}
