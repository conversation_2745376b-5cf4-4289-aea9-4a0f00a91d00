import { useState, useCallback, FormEvent, ChangeEvent } from 'react'
import { Link } from 'react-router-dom'
import '../../styles/pages/register.css'
import { validateUsername, validateEmail, validateFullName, validatePassword } from '../../utils/validation'
import { registerUser, parseApiError, getErrorField } from '../../utils/api'
import type { 
  FormState, 
  FieldStatus, 
  MessageState, 
  LoadingState,
  RegisterRequest,
  ApiErrorResponse 
} from '../../types/auth'

function RegisterPage() {
  // 表單狀態
  const [formState, setFormState] = useState<FormState>({
    username: { value: '', status: 'idle', errors: [] },
    email: { value: '', status: 'idle', errors: [] },
    fullName: { value: '', status: 'idle', errors: [] },
    password: { 
      value: '', 
      status: 'idle', 
      errors: [],
      requirements: {
        length: false,
        uppercase: false,
        lowercase: false,
        number: false,
        special: false
      }
    }
  })

  // 載入狀態
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false
  })

  // 訊息狀態
  const [messageState, setMessageState] = useState<MessageState>({
    type: 'info',
    message: '',
    visible: false
  })

  // 更新欄位值
  const updateField = useCallback((
    field: keyof FormState,
    value: string
  ) => {
    setFormState(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        value
      }
    }))
  }, [])

  // 驗證欄位
  const validateField = useCallback((
    field: keyof FormState,
    value: string
  ) => {
    let errors: string[] = []
    let status: FieldStatus = 'idle'
    let requirements = formState[field].requirements

    switch (field) {
      case 'username':
        errors = validateUsername(value)
        break
      case 'email':
        errors = validateEmail(value)
        break
      case 'fullName':
        errors = validateFullName(value)
        break
      case 'password':
        const validation = validatePassword(value)
        errors = validation.errors
        requirements = validation.requirements
        break
    }

    status = errors.length === 0 && value ? 'valid' : errors.length > 0 ? 'invalid' : 'idle'

    setFormState(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        errors,
        status,
        requirements
      }
    }))
  }, [formState])

  // 處理輸入變化
  const handleInputChange = useCallback((
    field: keyof FormState,
    event: ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value
    updateField(field, value)
    validateField(field, value)
  }, [updateField, validateField])

  // 顯示訊息
  const showMessage = useCallback((type: MessageState['type'], message: string) => {
    setMessageState({
      type,
      message,
      visible: true
    })
  }, [])

  // 隱藏訊息
  const hideMessage = useCallback(() => {
    setMessageState(prev => ({
      ...prev,
      visible: false
    }))
  }, [])

  // 處理表單提交
  const handleSubmit = useCallback(async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    hideMessage()

    // 驗證所有欄位
    const fields = ['username', 'email', 'fullName', 'password'] as const
    fields.forEach(field => {
      validateField(field, formState[field].value)
    })

    // 檢查是否有驗證錯誤
    const hasErrors = fields.some(field => formState[field].errors.length > 0)
    if (hasErrors) {
      showMessage('error', '請修正表單中的錯誤後再提交')
      return
    }

    // 準備註冊數據
    const registerData: RegisterRequest = {
      username: formState.username.value.trim(),
      email: formState.email.value.trim(),
      password: formState.password.value,
      full_name: formState.fullName.value.trim()
    }

    // 開始註冊流程
    setLoadingState({ isLoading: true, message: '正在建立帳戶...' })

    try {
      const result = await registerUser(registerData)

      if (result.success) {
        // 註冊成功
        showMessage('success', '帳戶建立成功！歡迎加入即時翻譯系統')
        
        // 重置表單
        setFormState({
          username: { value: '', status: 'idle', errors: [] },
          email: { value: '', status: 'idle', errors: [] },
          fullName: { value: '', status: 'idle', errors: [] },
          password: { 
            value: '', 
            status: 'idle', 
            errors: [],
            requirements: {
              length: false,
              uppercase: false,
              lowercase: false,
              number: false,
              special: false
            }
          }
        })

        // 3秒後跳轉到登入頁面
        setTimeout(() => {
          // 這裡可以導航到登入頁面
          window.location.href = '/login'
        }, 3000)

      } else {
        // 註冊失敗
        const errorResponse = result as ApiErrorResponse
        const errorMessage = parseApiError(errorResponse)
        const errorField = getErrorField(errorResponse)

        // 更新對應欄位的錯誤狀態
        if (errorField && errorField in formState) {
          setFormState(prev => ({
            ...prev,
            [errorField]: {
              ...prev[errorField as keyof FormState],
              status: 'invalid',
              errors: [errorMessage]
            }
          }))
        }

        showMessage('error', errorMessage)
      }
    } catch (error) {
      console.error('Registration error:', error)
      showMessage('error', '發生未預期的錯誤，請稍後再試')
    } finally {
      setLoadingState({ isLoading: false })
    }
  }, [formState, validateField, showMessage, hideMessage])

  // 獲取欄位圖示
  const getFieldIcon = (field: keyof FormState): string => {
    switch (formState[field].status) {
      case 'valid':
        return 'check_circle'
      case 'invalid':
        return 'error'
      default:
        return ''
    }
  }

  // 獲取欄位 CSS 類別
  const getFieldClass = (field: keyof FormState): string => {
    const baseClass = 'form-input'
    const statusClass = formState[field].status !== 'idle' ? formState[field].status : ''
    return `${baseClass} ${statusClass}`.trim()
  }

  return (
    <main className="register-main">
      {/* 返回主頁按鈕 */}
      <Link to="/" className="nav-button">
        <span className="material-symbols-outlined">home</span>
        返回主頁
      </Link>

      <div className="register-container">
        <div className="register-card">
          {/* 標題區域 */}
          <header className="register-header">
            <h1 className="register-title">建立新帳戶</h1>
            <p className="register-subtitle">加入即時翻譯系統，開始您的多語言會議體驗</p>
          </header>

          {/* 註冊表單 */}
          <form className="register-form" onSubmit={handleSubmit}>
            {/* 訊息顯示 */}
            {messageState.visible && (
              <div className={`message ${messageState.type}-message show`}>
                {messageState.message}
              </div>
            )}

            {/* 用戶名 */}
            <div className="form-group">
              <label htmlFor="username" className="form-label required">用戶名</label>
              <div className="input-wrapper">
                <input
                  type="text"
                  id="username"
                  name="username"
                  className={getFieldClass('username')}
                  placeholder="請輸入用戶名 (3-50字符)"
                  value={formState.username.value}
                  onChange={(e) => handleInputChange('username', e)}
                  required
                  autoComplete="username"
                  disabled={loadingState.isLoading}
                />
                {getFieldIcon('username') && (
                  <span className={`input-icon material-symbols-outlined ${formState.username.status}`}>
                    {getFieldIcon('username')}
                  </span>
                )}
              </div>
              {formState.username.errors.length > 0 && (
                <div className="form-error show">
                  {formState.username.errors[0]}
                </div>
              )}
            </div>

            {/* 電子郵件 */}
            <div className="form-group">
              <label htmlFor="email" className="form-label required">電子郵件</label>
              <div className="input-wrapper">
                <input
                  type="email"
                  id="email"
                  name="email"
                  className={getFieldClass('email')}
                  placeholder="請輸入電子郵件地址"
                  value={formState.email.value}
                  onChange={(e) => handleInputChange('email', e)}
                  required
                  autoComplete="email"
                  disabled={loadingState.isLoading}
                />
                {getFieldIcon('email') && (
                  <span className={`input-icon material-symbols-outlined ${formState.email.status}`}>
                    {getFieldIcon('email')}
                  </span>
                )}
              </div>
              {formState.email.errors.length > 0 && (
                <div className="form-error show">
                  {formState.email.errors[0]}
                </div>
              )}
            </div>

            {/* 全名 */}
            <div className="form-group">
              <label htmlFor="fullName" className="form-label required">全名</label>
              <div className="input-wrapper">
                <input
                  type="text"
                  id="fullName"
                  name="full_name"
                  className={getFieldClass('fullName')}
                  placeholder="請輸入您的全名"
                  value={formState.fullName.value}
                  onChange={(e) => handleInputChange('fullName', e)}
                  required
                  autoComplete="name"
                  disabled={loadingState.isLoading}
                />
                {getFieldIcon('fullName') && (
                  <span className={`input-icon material-symbols-outlined ${formState.fullName.status}`}>
                    {getFieldIcon('fullName')}
                  </span>
                )}
              </div>
              {formState.fullName.errors.length > 0 && (
                <div className="form-error show">
                  {formState.fullName.errors[0]}
                </div>
              )}
            </div>

            {/* 密碼 */}
            <div className="form-group">
              <label htmlFor="password" className="form-label required">密碼</label>
              <div className="input-wrapper">
                <input
                  type="password"
                  id="password"
                  name="password"
                  className={getFieldClass('password')}
                  placeholder="請輸入密碼"
                  value={formState.password.value}
                  onChange={(e) => handleInputChange('password', e)}
                  required
                  autoComplete="new-password"
                  disabled={loadingState.isLoading}
                />
                {getFieldIcon('password') && (
                  <span className={`input-icon material-symbols-outlined ${formState.password.status}`}>
                    {getFieldIcon('password')}
                  </span>
                )}
              </div>
              {formState.password.errors.length > 0 && (
                <div className="form-error show">
                  {formState.password.errors[0]}
                </div>
              )}

              {/* 密碼要求 */}
              <div className="password-requirements">
                <div className={`requirement ${formState.password.requirements.length ? 'valid' : ''}`}>
                  <span className="requirement-icon material-symbols-outlined">
                    {formState.password.requirements.length ? 'check_circle' : 'radio_button_unchecked'}
                  </span>
                  <span>至少 8 個字符</span>
                </div>
                <div className={`requirement ${formState.password.requirements.uppercase ? 'valid' : ''}`}>
                  <span className="requirement-icon material-symbols-outlined">
                    {formState.password.requirements.uppercase ? 'check_circle' : 'radio_button_unchecked'}
                  </span>
                  <span>包含大寫字母</span>
                </div>
                <div className={`requirement ${formState.password.requirements.lowercase ? 'valid' : ''}`}>
                  <span className="requirement-icon material-symbols-outlined">
                    {formState.password.requirements.lowercase ? 'check_circle' : 'radio_button_unchecked'}
                  </span>
                  <span>包含小寫字母</span>
                </div>
                <div className={`requirement ${formState.password.requirements.number ? 'valid' : ''}`}>
                  <span className="requirement-icon material-symbols-outlined">
                    {formState.password.requirements.number ? 'check_circle' : 'radio_button_unchecked'}
                  </span>
                  <span>包含數字</span>
                </div>
                <div className={`requirement ${formState.password.requirements.special ? 'valid' : ''}`}>
                  <span className="requirement-icon material-symbols-outlined">
                    {formState.password.requirements.special ? 'check_circle' : 'radio_button_unchecked'}
                  </span>
                  <span>包含特殊字符</span>
                </div>
              </div>
            </div>

            {/* 提交按鈕 */}
            <button 
              type="submit" 
              className="submit-btn"
              disabled={loadingState.isLoading}
            >
              {loadingState.isLoading && (
                <span className="loading-spinner"></span>
              )}
              <span>
                {loadingState.isLoading ? '建立中...' : '建立帳戶'}
              </span>
            </button>

            {/* 登入連結 */}
            <div className="login-link">
              已有帳戶？ <Link to="/login">立即登入</Link>
            </div>
          </form>
        </div>
      </div>
    </main>
  )
}

export default RegisterPage
