import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import HostLogin from './components/pages/HostLogin'
import TranslatePage from './components/pages/TranslatePage'
import RegisterPage from './components/pages/RegisterPage'

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<HostLogin />} />
        <Route path="/register" element={<RegisterPage />} />
        <Route path="/happyeverytimes" element={<TranslatePage />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  )
}

export default App
