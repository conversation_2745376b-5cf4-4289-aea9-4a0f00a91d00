import { useState, useCallback, FormEvent } from 'react'
import { useNavigate, Link } from 'react-router-dom'
import '../host-login.css'

interface LanguageOption {
  value: string
  name: string
}

const LANGUAGE_OPTIONS: LanguageOption[] = [
  { value: 'en', name: 'English' },
  { value: 'ja', name: '日本語' },
  { value: 'zh', name: '繁體中文' },
  { value: 'ko', name: '한국어' },
  { value: 'fr', name: 'Français' },
  { value: 'de', name: '<PERSON><PERSON><PERSON>' },
  { value: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { value: 'it', name: 'Italiano' },
  { value: 'pt', name: 'Português' },
  { value: 'ru', name: 'Русский' }
]

function HostLogin() {
  const navigate = useNavigate()
  const [currentView, setCurrentView] = useState<'login' | 'language-select'>('login')
  const [password, setPassword] = useState('')
  const [selectedLanguages, setSelectedLanguages] = useState<LanguageOption[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  // 處理密碼登入
  const handlePasswordSubmit = useCallback(async (e: FormEvent) => {
    e.preventDefault()
    setError('')
    
    if (!password.trim()) {
      setError('請輸入密碼')
      return
    }
    
    if (password.length !== 6 || !/^\d{6}$/.test(password)) {
      setError('密碼必須是6位數字')
      return
    }
    
    setIsLoading(true)
    
    // 模擬 API 驗證
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 這裡可以加入實際的密碼驗證邏輯
      if (password === '123456') {
        setCurrentView('language-select')
      } else {
        setError('密碼錯誤，請重新輸入')
      }
    } catch (err) {
      setError('登入失敗，請稍後再試')
      console.log(err);
    } finally {
      setIsLoading(false)
    }
  }, [password])

  // 處理語言選擇
  const handleLanguageSelect = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = e.target.value
    if (!selectedValue) return
    
    const language = LANGUAGE_OPTIONS.find(lang => lang.value === selectedValue)
    if (!language) return
    
    // 檢查是否已經選擇過
    if (selectedLanguages.some(lang => lang.value === selectedValue)) {
      return
    }
    
    setSelectedLanguages(prev => [...prev, language])
    e.target.value = '' // 重置選擇框
  }, [selectedLanguages])

  // 移除已選擇的語言
  const removeLanguage = useCallback((valueToRemove: string) => {
    setSelectedLanguages(prev => prev.filter(lang => lang.value !== valueToRemove))
  }, [])

  // 確認語言選擇並進入翻譯頁面
  const handleLanguageConfirm = useCallback(async (e: FormEvent) => {
    e.preventDefault()
    
    if (selectedLanguages.length === 0) {
      setError('請至少選擇一種翻譯語言')
      return
    }
    
    setIsLoading(true)
    setError('')
    
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 將選擇的語言傳遞給翻譯頁面
      navigate('/happyeverytimes', {
        state: {
          selectedLanguages: selectedLanguages.map(lang => lang.value),
          meetingName: '會議名稱'
        }
      })
    } catch (err) {
      setError('進入會議失敗，請稍後再試')
      setIsLoading(false)
      console.log(err);
    }
  }, [selectedLanguages, navigate])

  return (
    <main className="host-login-main">
      {/* 導航到翻譯頁面的按鈕 */}
      <Link to="/happyeverytimes" className="nav-button">
        <span className="material-symbols-outlined">translate</span>
        直接進入翻譯
      </Link>

      {/* 登入視圖 */}
      {currentView === 'login' && (
        <section className="card">
          <header className="card-header">
            <h1 className="card-title">會議名稱</h1>
          </header>
          <form onSubmit={handlePasswordSubmit}>
            <div className="form-group">
              <div className="text-input-container">
                <input
                  className={`input-text ${error ? 'input-error' : ''}`}
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="請輸入密碼（6位數字）"
                  maxLength={6}
                  required
                  disabled={isLoading}
                />
              </div>
              {error && (
                <div className="error-message">
                  <span className="material-symbols-outlined">error</span>
                  {error}
                </div>
              )}
            </div>
            <button 
              type="submit" 
              className={`btn btn-primary ${isLoading ? 'loading' : ''}`}
              disabled={isLoading}
            >
              {isLoading ? '驗證中...' : '確認'}
            </button>
          </form>
        </section>
      )}

      {/* 語言選擇視圖 */}
      {currentView === 'language-select' && (
        <section className="card">
          <header className="card-header">
            <h1 className="card-title">會議名稱</h1>
          </header>
          <form onSubmit={handleLanguageConfirm}>
            <div className="form-group">
              <div className="search-input-container">
                <span className="material-symbols-outlined search-icon">search</span>
                <select 
                  className="input-select input-select-with-search"
                  onChange={handleLanguageSelect}
                  disabled={isLoading}
                  defaultValue=""
                >
                  <option value="" disabled>請選擇翻譯語言</option>
                  {LANGUAGE_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className={`selected-languages ${selectedLanguages.length > 0 ? 'has-languages' : ''}`}>
                {selectedLanguages.map(language => (
                  <div key={language.value} className="language-chip">
                    {language.name}
                    <button
                      type="button"
                      className="language-chip-remove"
                      onClick={() => removeLanguage(language.value)}
                      aria-label={`移除 ${language.name}`}
                    >
                      <span className="material-symbols-outlined">close</span>
                    </button>
                  </div>
                ))}
              </div>
              
              {error && (
                <div className="error-message">
                  <span className="material-symbols-outlined">error</span>
                  {error}
                </div>
              )}
            </div>
            
            <button 
              type="submit" 
              className={`btn btn-primary ${isLoading ? 'loading' : ''}`}
              disabled={isLoading}
            >
              {isLoading ? '進入中...' : '確認'}
            </button>
          </form>
        </section>
      )}
    </main>
  )
}

export default HostLogin
